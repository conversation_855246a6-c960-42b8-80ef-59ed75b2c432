package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 考题选项表 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_question_option")
@KeySequence("publicbiz_question_option_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionOptionDO extends BaseDO {

    /**
     * 主键，自增
     */
    @TableId
    private Long id;
    /**
     * 租户ID
     */
    private Long tenantId;

    // ==================== 关联信息 ====================
    /**
     * 考题ID，关联publicbiz_question表
     */
    private Long questionId;

    // ==================== 选项信息 ====================
    /**
     * 选项类型：choice-选择项，match_left-匹配左列，match_right-匹配右列
     */
    private String optionType;
    /**
     * 选项标识，如：A、B、C、D或1、2、3、4
     */
    private String optionKey;
    /**
     * 选项内容
     */
    private String optionContent;
    /**
     * 是否正确答案：0-否，1-是
     */
    private Integer isCorrect;
    /**
     * 排序序号
     */
    private Integer sortOrder;

    // ==================== 匹配题专用字段 ====================
    /**
     * 匹配目标，用于匹配题记录对应关系
     */
    private String matchTarget;
}

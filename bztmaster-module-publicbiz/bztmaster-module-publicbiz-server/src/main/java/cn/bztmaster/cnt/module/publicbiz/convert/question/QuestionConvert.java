package cn.bztmaster.cnt.module.publicbiz.convert.question;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.question.dto.*;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionCategoryDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 考题管理 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionConvert {

    QuestionConvert INSTANCE = Mappers.getMapper(QuestionConvert.class);

    QuestionDO convert(QuestionSaveReqVO bean);

    QuestionDO convert(QuestionSaveReqDTO bean);

    QuestionRespVO convert(QuestionDO bean);

    QuestionRespDTO convertDTO(QuestionDO bean);

    List<QuestionRespVO> convertList(List<QuestionDO> list);

    List<QuestionRespDTO> convertDTOList(List<QuestionDO> list);

    PageResult<QuestionRespVO> convertPage(PageResult<QuestionDO> page);

    PageResult<QuestionRespDTO> convertDTOPage(PageResult<QuestionDO> page);

    // ==================== 考题分类相关转换 ====================

    QuestionCategoryDO convert(QuestionCategorySaveReqVO bean);

    QuestionCategoryDO convert(QuestionCategorySaveReqDTO bean);

    QuestionCategoryRespVO convertCategory(QuestionCategoryDO bean);

    QuestionCategoryRespDTO convertCategoryDTO(QuestionCategoryDO bean);

    List<QuestionCategoryRespVO> convertCategoryList(List<QuestionCategoryDO> list);

    List<QuestionCategoryRespDTO> convertCategoryDTOList(List<QuestionCategoryDO> list);

    // ==================== DTO 和 VO 之间的转换 ====================

    QuestionSaveReqDTO convertToDTO(QuestionSaveReqVO bean);

    QuestionPageReqDTO convertToDTO(QuestionPageReqVO bean);

    QuestionCategorySaveReqDTO convertCategoryToDTO(QuestionCategorySaveReqVO bean);
}

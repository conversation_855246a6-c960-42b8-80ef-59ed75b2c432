package cn.bztmaster.cnt.module.publicbiz.service.question.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.question.QuestionConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionCategoryDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.question.QuestionCategoryMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.question.QuestionMapper;
import cn.bztmaster.cnt.module.publicbiz.service.question.QuestionService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;

/**
 * 考题管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionServiceImpl implements QuestionService {

    @Resource
    private QuestionMapper questionMapper;

    @Resource
    private QuestionCategoryMapper questionCategoryMapper;

    @Override
    public Long createQuestion(QuestionSaveReqVO createReqVO) {
        // 插入
        QuestionDO question = QuestionConvert.INSTANCE.convert(createReqVO);
        questionMapper.insert(question);
        // 返回
        return question.getId();
    }

    @Override
    public void updateQuestion(QuestionSaveReqVO updateReqVO) {
        // 校验存在
        validateQuestionExists(updateReqVO.getId());
        // 更新
        QuestionDO updateObj = QuestionConvert.INSTANCE.convert(updateReqVO);
        questionMapper.updateById(updateObj);
    }

    @Override
    public void deleteQuestion(Long id) {
        // 校验存在
        validateQuestionExists(id);
        // 删除
        questionMapper.deleteById(id);
    }

    private void validateQuestionExists(Long id) {
        if (questionMapper.selectById(id) == null) {
            throw exception(QUESTION_NOT_EXISTS);
        }
    }

    @Override
    public QuestionDO getQuestion(Long id) {
        return questionMapper.selectById(id);
    }

    @Override
    public List<QuestionDO> getQuestionList(Collection<Long> ids) {
        return questionMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<QuestionDO> getQuestionPage(QuestionPageReqVO pageReqVO) {
        return questionMapper.selectPage(pageReqVO);
    }

    // ==================== 考题分类相关方法 ====================

    @Override
    public Long createQuestionCategory(QuestionCategorySaveReqVO createReqVO) {
        // 插入
        QuestionCategoryDO category = QuestionConvert.INSTANCE.convert(createReqVO);
        questionCategoryMapper.insert(category);
        // 返回
        return category.getId();
    }

    @Override
    public void deleteQuestionCategory(Long id) {
        // 校验存在
        validateQuestionCategoryExists(id);
        // 删除
        questionCategoryMapper.deleteById(id);
    }

    private void validateQuestionCategoryExists(Long id) {
        if (questionCategoryMapper.selectById(id) == null) {
            throw exception(QUESTION_CATEGORY_NOT_EXISTS);
        }
    }

    @Override
    public PageResult<QuestionCategoryRespVO> getQuestionCategoryList(String level1, String biz) {
        List<QuestionCategoryDO> list = questionCategoryMapper.selectList(level1, biz);
        return new PageResult<>(BeanUtils.toBean(list, QuestionCategoryRespVO.class), (long) list.size());
    }
}

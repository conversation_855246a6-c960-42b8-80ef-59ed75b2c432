# 考题管理模块代码说明

## 概述
本模块为考题管理功能的完整后端实现，基于接口文档和前端页面分析生成，包含API和Server两个子模块的完整分层结构。

## 模块结构

### API模块 (bztmaster-module-publicbiz-api)
```
src/main/java/cn/bztmaster/cnt/module/publicbiz/
├── api/question/
│   ├── QuestionApi.java                    # 考题管理API接口
│   └── dto/
│       ├── QuestionRespDTO.java            # 考题响应DTO
│       ├── QuestionSaveReqDTO.java         # 考题保存请求DTO
│       ├── QuestionPageReqDTO.java         # 考题分页查询DTO
│       ├── QuestionCategoryRespDTO.java    # 考题分类响应DTO
│       └── QuestionCategorySaveReqDTO.java # 考题分类保存请求DTO
└── enums/question/
    ├── QuestionTypeEnum.java               # 题型枚举
    ├── QuestionBusinessModuleEnum.java     # 业务模块枚举
    ├── QuestionDifficultyEnum.java         # 难度等级枚举
    ├── QuestionOptionTypeEnum.java         # 选项类型枚举
    └── QuestionStatusEnum.java             # 状态枚举
```

### Server模块 (bztmaster-module-publicbiz-server)
```
src/main/java/cn/bztmaster/cnt/module/publicbiz/
├── controller/admin/question/
│   ├── QuestionController.java             # 考题管理Controller
│   └── vo/
│       ├── QuestionRespVO.java             # 考题响应VO
│       ├── QuestionSaveReqVO.java          # 考题保存请求VO
│       ├── QuestionPageReqVO.java          # 考题分页查询VO
│       ├── QuestionCategoryRespVO.java     # 考题分类响应VO
│       └── QuestionCategorySaveReqVO.java  # 考题分类保存请求VO
├── service/question/
│   ├── QuestionService.java                # 考题管理Service接口
│   └── impl/
│       └── QuestionServiceImpl.java        # 考题管理Service实现
├── dal/
│   ├── dataobject/question/
│   │   ├── QuestionDO.java                 # 考题主表DO
│   │   ├── QuestionCategoryDO.java         # 考题分类表DO
│   │   └── QuestionOptionDO.java           # 考题选项表DO
│   └── mysql/question/
│       ├── QuestionMapper.java             # 考题主表Mapper
│       ├── QuestionCategoryMapper.java     # 考题分类表Mapper
│       └── QuestionOptionMapper.java       # 考题选项表Mapper
└── convert/question/
    └── QuestionConvert.java                # 对象转换类
```

### 资源文件
```
src/main/resources/
├── sql/
│   └── question_tables.sql                # 数据库表结构SQL
└── mapper/
    ├── QuestionMapper.xml                  # 考题主表XML映射（待生成）
    ├── QuestionCategoryMapper.xml          # 考题分类表XML映射（待生成）
    └── QuestionOptionMapper.xml            # 考题选项表XML映射（待生成）
```

## 功能特性

### 1. 考题管理
- ✅ 新增考题 (POST /publicbiz/question/create)
- ✅ 更新考题 (POST /publicbiz/question/update)
- ✅ 删除考题 (DELETE /publicbiz/question/delete)
- ✅ 考题详情 (GET /publicbiz/question/detail)
- ✅ 考题分页查询 (GET /publicbiz/question/page)

### 2. 考题分类管理
- ✅ 新增分类 (POST /publicbiz/question/category/create)
- ✅ 删除分类 (DELETE /publicbiz/question/category/delete)
- ✅ 分类列表查询 (GET /publicbiz/question/category/list)

### 3. 支持的题型
- 单选题、多选题、判断题
- 简答题、填空题、材料题
- 排序题、匹配题、文件上传题

### 4. 业务模块
- 家政业务、高校业务
- 培训业务、认证业务

## 数据库表结构

### 1. 考题主表 (publicbiz_question)
存储考题的基本信息、题干、答案等核心数据，包含分类信息、认定点信息、题目内容、业务分类和扩展字段。

### 2. 考题分类表 (publicbiz_question_category)
存储考题的分类层级结构和代码信息，支持三级分类体系。

### 3. 考题选项表 (publicbiz_question_option)
存储选择题、匹配题、排序题等的选项信息，支持多种选项类型。

## 权限控制
所有接口都配置了相应的权限控制：
- `publicbiz:question:create` - 新增考题权限
- `publicbiz:question:update` - 更新考题权限
- `publicbiz:question:delete` - 删除考题权限
- `publicbiz:question:query` - 查询考题权限
- `publicbiz:question:category:*` - 分类管理相关权限

## 错误码定义
在 `ErrorCodeConstants` 中定义了考题管理相关的错误码：
- `QUESTION_NOT_EXISTS` (1_003_007_000) - 考题不存在
- `QUESTION_CATEGORY_NOT_EXISTS` (1_003_007_001) - 考题分类不存在
- `QUESTION_TYPE_INVALID` (1_003_007_002) - 考题类型无效
- `QUESTION_DIFFICULTY_INVALID` (1_003_007_003) - 考题难度等级无效
- `QUESTION_BUSINESS_MODULE_INVALID` (1_003_007_004) - 考题业务模块无效

## 使用说明

### 1. 数据库初始化
执行 `src/main/resources/sql/question_tables.sql` 中的SQL语句创建相关数据表。

### 2. 接口调用
所有接口都遵循RESTful风格，支持标准的CRUD操作。

### 3. 前端对接
Controller层的VO对象与前端页面字段完全对应，可直接用于前后端数据交互。

## 待完善项目

### 1. XML映射文件
需要根据具体的复杂查询需求创建对应的XML映射文件。

### 2. 批量导入功能
前端页面中提到的批量导入功能，需要额外实现文件上传和解析逻辑。

### 3. 操作日志
需要集成操作日志记录功能，记录考题的增删改操作。

### 4. 统计功能
前端页面中的统计卡片数据，需要实现相应的统计查询接口。

## 技术栈
- Spring Boot
- MyBatis Plus
- MapStruct (对象转换)
- Swagger (API文档)
- Spring Security (权限控制)
- MySQL (数据存储)

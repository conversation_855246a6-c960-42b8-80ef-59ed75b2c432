package cn.bztmaster.cnt.module.publicbiz.enums.question;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考题状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum QuestionStatusEnum {

    DISABLED(0, "禁用"),
    ENABLED(1, "启用");

    /**
     * 状态值
     */
    private final Integer status;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据状态值获取枚举
     *
     * @param status 状态值
     * @return 枚举
     */
    public static QuestionStatusEnum getByStatus(Integer status) {
        if (status == null) {
            return null;
        }
        for (QuestionStatusEnum value : QuestionStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }
}

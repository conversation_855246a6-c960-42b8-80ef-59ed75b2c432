package cn.bztmaster.cnt.module.publicbiz.enums.question;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考题选项类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum QuestionOptionTypeEnum {

    CHOICE("choice", "选择项"),
    MATCH_LEFT("match_left", "匹配左列"),
    MATCH_RIGHT("match_right", "匹配右列");

    /**
     * 选项类型值
     */
    private final String type;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据选项类型值获取枚举
     *
     * @param type 选项类型值
     * @return 枚举
     */
    public static QuestionOptionTypeEnum getByType(String type) {
        if (type == null) {
            return null;
        }
        for (QuestionOptionTypeEnum value : QuestionOptionTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}

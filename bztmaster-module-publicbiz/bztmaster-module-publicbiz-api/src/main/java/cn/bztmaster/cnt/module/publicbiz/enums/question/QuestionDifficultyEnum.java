package cn.bztmaster.cnt.module.publicbiz.enums.question;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考题难度等级枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum QuestionDifficultyEnum {

    EASY(1, "简单"),
    MEDIUM(2, "中等"),
    HARD(3, "困难");

    /**
     * 难度等级
     */
    private final Integer level;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据难度等级获取枚举
     *
     * @param level 难度等级
     * @return 枚举
     */
    public static QuestionDifficultyEnum getByLevel(Integer level) {
        if (level == null) {
            return null;
        }
        for (QuestionDifficultyEnum value : QuestionDifficultyEnum.values()) {
            if (value.getLevel().equals(level)) {
                return value;
            }
        }
        return null;
    }
}

package cn.bztmaster.cnt.module.publicbiz.enums;

import cn.bztmaster.cnt.framework.common.exception.ErrorCode;

/**
 * PublicBiz 错误码枚举类
 * <p>
 * publicbiz 系统，使用 1-003-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 商机管理 1-003-001-000 ==========
    ErrorCode BUSINESS_NOT_EXISTS = new ErrorCode(1_003_001_000, "商机不存在");

    // ========== 商机跟进 1-003-002-000 ==========
    ErrorCode BUSINESS_FOLLOWUP_NOT_EXISTS = new ErrorCode(1_003_002_000, "商机跟进不存在");

    // ========== 商机日志 1-003-003-000 ==========
    ErrorCode BUSINESS_LOG_NOT_EXISTS = new ErrorCode(1_003_003_000, "商机日志不存在");

    // ========== 线索中心 1-003-004-000 ==========
    ErrorCode LEADS_NOT_EXISTS = new ErrorCode(1_003_004_000, "线索不存在");

    // ========== 合作伙伴 1-003-005-000 ==========
    ErrorCode PARTNER_NOT_EXISTS = new ErrorCode(1_003_005_000, "合作伙伴不存在");

    // ========== 数字资产课程 1-003-006-000 ==========
    ErrorCode DIGITAL_ASSET_COURSE_NOT_EXISTS = new ErrorCode(1_003_006_000, "数字资产课程不存在");

    // ========== 考题管理 1-003-007-000 ==========
    ErrorCode QUESTION_NOT_EXISTS = new ErrorCode(1_003_007_000, "考题不存在");
    ErrorCode QUESTION_CATEGORY_NOT_EXISTS = new ErrorCode(1_003_007_001, "考题分类不存在");
    ErrorCode QUESTION_TYPE_INVALID = new ErrorCode(1_003_007_002, "考题类型无效");
    ErrorCode QUESTION_DIFFICULTY_INVALID = new ErrorCode(1_003_007_003, "考题难度等级无效");
    ErrorCode QUESTION_BUSINESS_MODULE_INVALID = new ErrorCode(1_003_007_004, "考题业务模块无效");
}

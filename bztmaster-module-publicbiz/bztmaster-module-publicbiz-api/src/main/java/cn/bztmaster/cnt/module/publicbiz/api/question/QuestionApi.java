package cn.bztmaster.cnt.module.publicbiz.api.question;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.question.dto.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 考题管理 API 接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "publicbiz-service")
@Tag(name = "RPC 服务 - 考题管理")
public interface QuestionApi {

    String PREFIX = "/publicbiz/question";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "通过考题ID查询考题")
    @Parameter(name = "id", description = "考题编号", example = "1", required = true)
    CommonResult<QuestionRespDTO> getQuestion(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "通过考题ID查询考题们")
    @Parameter(name = "ids", description = "考题编号数组", example = "1,2", required = true)
    CommonResult<List<QuestionRespDTO>> getQuestionList(@RequestParam("ids") Collection<Long> ids);

    @PostMapping(PREFIX + "/page")
    @Operation(summary = "考题分页查询")
    CommonResult<PageResult<QuestionRespDTO>> getQuestionPage(@Valid @RequestBody QuestionPageReqDTO pageReqDTO);

    @PostMapping(PREFIX + "/create")
    @Operation(summary = "新增考题")
    CommonResult<Long> createQuestion(@Valid @RequestBody QuestionSaveReqDTO saveReqDTO);

    @PostMapping(PREFIX + "/update")
    @Operation(summary = "更新考题")
    CommonResult<Boolean> updateQuestion(@Valid @RequestBody QuestionSaveReqDTO saveReqDTO);

    @DeleteMapping(PREFIX + "/delete")
    @Operation(summary = "删除考题")
    @Parameter(name = "id", description = "考题编号", example = "1", required = true)
    CommonResult<Boolean> deleteQuestion(@RequestParam("id") Long id);

    // 考题分类相关接口
    @PostMapping(PREFIX + "/category/create")
    @Operation(summary = "新增考题分类")
    CommonResult<Long> createQuestionCategory(@Valid @RequestBody QuestionCategorySaveReqDTO saveReqDTO);

    @DeleteMapping(PREFIX + "/category/delete")
    @Operation(summary = "删除考题分类")
    @Parameter(name = "id", description = "分类编号", example = "1", required = true)
    CommonResult<Boolean> deleteQuestionCategory(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/category/list")
    @Operation(summary = "查询考题分类列表")
    CommonResult<List<QuestionCategoryRespDTO>> getQuestionCategoryList(
            @RequestParam(value = "level1", required = false) String level1,
            @RequestParam(value = "biz", required = false) String biz);
}

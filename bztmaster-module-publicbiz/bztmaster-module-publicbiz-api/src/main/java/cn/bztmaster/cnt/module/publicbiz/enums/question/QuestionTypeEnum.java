package cn.bztmaster.cnt.module.publicbiz.enums.question;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考题题型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum QuestionTypeEnum {

    SINGLE_CHOICE("单选题", "单选题"),
    MULTIPLE_CHOICE("多选题", "多选题"),
    TRUE_FALSE("判断题", "判断题"),
    SHORT_ANSWER("简答题", "简答题"),
    FILL_BLANK("填空题", "填空题"),
    MATERIAL("材料题", "材料题"),
    SORTING("排序题", "排序题"),
    MATCHING("匹配题", "匹配题"),
    FILE_UPLOAD("文件上传题", "文件上传题");

    /**
     * 题型值
     */
    private final String type;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据题型值获取枚举
     *
     * @param type 题型值
     * @return 枚举
     */
    public static QuestionTypeEnum getByType(String type) {
        if (type == null) {
            return null;
        }
        for (QuestionTypeEnum value : QuestionTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
